# PubMed文献检索系统依赖包
# 安装命令: pip3 install -r requirements.txt

# AI模型相关
google-generativeai>=0.3.0    # Google Gemini AI API客户端

# 环境配置
python-dotenv>=1.0.0          # 环境变量管理

# 网络请求和重试
requests>=2.28.0              # HTTP请求库
tenacity>=8.2.0               # 智能重试机制

# 数据处理
pandas>=1.5.0                 # 数据分析和处理
openpyxl>=3.1.0               # Excel文件读写

# 可选依赖（用于性能监控和调试）
# psutil>=5.9.0               # 系统资源监控
# tqdm>=4.64.0                # 进度条显示
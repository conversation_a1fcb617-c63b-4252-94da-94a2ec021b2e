# PubMed文献检索工具 - 傻瓜式上手指南

欢迎使用！本工具是一个智能小助手，能帮您自动完成繁琐的PubMed文献检索工作。您只需要提出一个研究问题，它就能为您搜索、筛选、整理文献，并最终生成一份带期刊分区和影响因子的精美Excel报告。

工具特色：
- AI智能生成PICO标准和检索式
- 自动搜索PubMed数据库
- 智能筛选相关文献
- 自动添加期刊影响因子和分区信息
- 生成专业Excel报告
- 高效避免重复检索，节省时间

---
## 一、 首次安装 (只需做一次，约5-10分钟)
---

在开始使用前，我们需要为电脑进行一次简单的"装修"。

### 快速安装（推荐）
如果您想要最简单的安装方式：
1. 打开终端（Mac）或命令提示符（Windows）
2. 进入项目文件夹：cd 您的项目路径
3. 运行安装脚本：./setup.sh
4. 按照提示完成配置即可！

### 手动安装步骤

#### 1. 安装Python (如果电脑里没有的话)
Python是本工具运行的基础。
- 如何检查？ 打开终端（Mac）或命令提示符（Windows），输入 python3 --version 然后回车。如果显示出版本号（如 Python 3.9.6），说明已安装，可跳过此步。
- 如何安装？ 访问官网 https://www.python.org/downloads/，下载并安装最新版本。安装时，请务必勾选 "Add Python to PATH" 或类似的选项。

#### 2. 配置您的专属"通行证" (API密钥)
这个"通行证"是让工具能够使用强大AI模型的关键。

获取Google Gemini API密钥：
1. 访问 https://aistudio.google.com/app/apikey
2. 登录您的Google账号
3. 点击"Create API Key"创建新密钥
4. 复制生成的API密钥（以AIzaSy开头的长字符串）

配置密钥：
- 在本项目文件夹里，找到一个名为 .env 的文件。如果找不到，可以复制一份 .env.example 文件并将其重命名为 .env。
- 用记事本或任何文本编辑器打开 .env 文件。
- 将文件中的 your_key_here 替换为您自己的Google Gemini API密钥。它看起来会是这样：
  GEMINI_API_KEY=AIzaSy... (一长串字符)
- 保存并关闭文件。

#### 3. 安装所有"零件" (依赖库)
这些"零件"是工具运行时需要用到的小工具包。
- 打开终端或命令提示符。
- 关键一步: 使用 cd 命令进入本项目的文件夹。例如，如果项目在桌面上，您可以输入 cd Desktop/pubmed_meta_search-袁泽 然后回车。
- 复制并粘贴以下命令，然后按回车键：
  pip3 install -r 说明文档/requirements.txt
- 屏幕上会滚动很多安装信息，请耐心等待它完成。

#### 4. 设置文件权限（Mac/Linux用户）
如果您使用Mac或Linux系统：
chmod +x main.py
chmod +x setup.sh

---
## 二、 开始使用 (每次都这样做)
---

"装修"完成后，每次使用就非常简单了！

### 1. 打开终端并进入项目目录
像安装时一样，打开终端或命令提示符，并用 cd 命令进入本项目的文件夹。

### 2. 下达指令
- 指令格式: python3 main.py "您的研究问题"
- 说明:
  - python3 main.py 是启动命令。
  - 后面空一格，然后用英文双引号 "" 把您的研究问题完整地包起来。
- 示例:
  # 示例1：眼科研究
  python3 main.py "检索雷珠单抗治疗nAMD的所有临床研究"

  # 示例2：肿瘤研究
  python3 main.py "PD-1抑制剂治疗非小细胞肺癌的疗效和安全性"

  # 示例3：心血管研究
  python3 main.py "他汀类药物对冠心病患者预后的影响"

- 输入完命令后，按回车键，工具就会开始自动工作。

### 3. 工作流程说明
工具会按以下步骤自动工作：
1. AI分析问题 - 理解您的研究问题
2. 生成PICO标准 - 制定科学的文献筛选标准
3. 生成检索式 - 创建专业的PubMed搜索语句
4. 搜索文献 - 在PubMed数据库中搜索相关文献
5. 智能筛选 - 根据PICO标准筛选最相关的文献
6. 数据整理 - 提取关键信息并添加期刊数据
7. 生成报告 - 创建专业的Excel报告

### 4. 查看结果
- 工具开始运行后，您会在终端窗口看到实时的工作日志。
- 根据您的问题复杂度和网络情况，整个过程可能需要几分钟到几十分钟不等。
- 任务完成的标志: 当终端不再滚动日志，并回到您可以输入命令的状态时，就表示任务完成了。
- 最终成果: 在项目文件夹中，找到一个名为 pico_results.xlsx 的Excel文件。所有符合条件的文献，连同期刊信息和PubMed链接，都已为您整齐地排列在这份报告中。

---
## 三、 常见问题解答
---

### Q1: 为什么运行时提示"Permission denied"？
A: 这是文件权限问题。请运行：chmod +x main.py 或者直接运行 ./setup.sh

### Q2: 为什么提示找不到某些Python包？
A: 请确保已安装所有依赖：pip3 install -r 说明文档/requirements.txt

### Q3: 可以同时运行多个查询吗？
A: 建议一次只运行一个查询，避免API调用冲突。

### Q4: 生成的Excel文件在哪里？
A: 在项目根目录下，文件名为 pico_results.xlsx

### Q5: 如何获得更好的检索结果？
A: 
- 使用具体、明确的研究问题
- 包含关键的医学术语
- 避免过于宽泛的描述

### Q6: 工具支持哪些语言？
A: 支持中文和英文研究问题，输出结果为中文。

---
## 四、 技术支持
---

如果遇到技术问题，请：
1. 检查网络连接是否正常
2. 确认API密钥是否正确配置
3. 查看终端输出的错误信息
4. 参考技术文档 TECHNICAL_GUIDE.md 获取更详细的信息

祝您使用愉快！如有问题，请查看技术文档或联系开发者。

import logging
import sys
import csv
import os
from logging.handlers import RotatingFileHandler

def setup_logging():
    """
    Configures the logging for the application.

    This setup directs logs to both the console and a rotating file (`app.log`).
    It establishes a clear format for log messages, including timestamp,
    log level, and the message itself.

    - Logs with level INFO and above will be recorded.
    - The log file is set to rotate after reaching 1MB, keeping up to 5 backups.
    """
    # Create a logger object
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Prevent the root logger from propagating messages to the console twice
    if logger.hasHandlers():
        logger.handlers.clear()

    # Create a formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # --- Console <PERSON> ---
    # Outputs logs to the standard system output (your terminal)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # --- File Handler ---
    # Writes logs to a file, with rotation to manage file size
    # The file will be created in the directory where the main script is run.
    file_handler = RotatingFileHandler(
        'app.log', maxBytes=1024*1024, backupCount=5, encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    logging.info("Logging configured successfully.")

def load_retrieved_pmids(filepath: str) -> set:
    """
    Loads previously retrieved PMIDs from a CSV file into a set.

    Args:
        filepath: The path to the CSV file.

    Returns:
        A set containing all PMIDs from the file. Returns an empty set
        if the file does not exist or is empty.
    """
    if not os.path.exists(filepath):
        logging.info(f"PMID file not found at '{filepath}'. Starting with an empty set.")
        return set()
    
    try:
        with open(filepath, mode='r', newline='', encoding='utf-8') as infile:
            reader = csv.reader(infile)
            header = next(reader, None)
            if header != ['PMID']:
                logging.warning(f"PMID file at '{filepath}' has an incorrect header or is empty. Starting fresh.")
                return set()
            pmids = {row[0] for row in reader if row}
            logging.info(f"Successfully loaded {len(pmids)} PMIDs from '{filepath}'.")
            return pmids
    except (IOError, csv.Error) as e:
        logging.error(f"Could not read existing PMID file at '{filepath}'. Starting fresh. Error: {e}")
        return set()

def save_retrieved_pmids(filepath: str, pmids: set):
    """
    Saves a set of PMIDs to a CSV file, overwriting any existing content.

    Args:
        filepath: The path to the CSV file.
        pmids: A set of PMIDs to save.
    """
    try:
        with open(filepath, mode='w', newline='', encoding='utf-8') as outfile:
            writer = csv.writer(outfile)
            writer.writerow(['PMID'])  # Write header
            for pmid in sorted(list(pmids)): # Save in sorted order for consistency
                writer.writerow([pmid])
        logging.info(f"Successfully saved {len(pmids)} PMIDs to '{filepath}'.")
    except IOError as e:
        logging.error(f"Could not write to PMID file at '{filepath}'. Error: {e}")
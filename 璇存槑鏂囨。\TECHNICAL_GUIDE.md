# PubMed文献检索系统 - 技术指南

## 📋 目录
1. [系统概述](#1-系统概述)
2. [核心架构](#2-核心架构)
3. [模块详解](#3-模块详解)
4. [工作流程](#4-工作流程)
5. [AI模型配置](#5-ai模型配置)
6. [数据处理](#6-数据处理)
7. [性能优化](#7-性能优化)
8. [故障排除](#8-故障排除)
9. [开发指南](#9-开发指南)

---

## 1. 系统概述

### 1.1 设计理念
本系统采用**"两阶段智能策略"**，实现高效的PubMed文献检索和筛选：

- **🎯 智能检索**: 使用AI生成精准的PubMed检索式
- **📊 智能筛选**: 基于PICO标准进行文献相关性评估
- **⚡ 效率优化**: 避免重复检索，支持增量更新
- **🔄 自适应机制**: 根据结果动态调整策略

### 1.2 核心特性
- **AI驱动**: 集成Google Gemini 2.5模型
- **PICO标准**: 基于循证医学的文献筛选
- **并发处理**: 多线程文献筛选，提升效率
- **数据丰富**: 自动添加期刊影响因子和分区
- **专业输出**: 生成格式化Excel报告

### 1.3 技术栈
```
Frontend: 命令行界面 (CLI)
Backend: Python 3.9+
AI Engine: Google Gemini 2.5-pro / 2.5-flash-lite
Database: PubMed E-utilities API
Data Processing: pandas, openpyxl
Concurrency: ThreadPoolExecutor
```

---

## 2. 核心架构

### 2.1 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户输入      │───▶│   AI分析引擎    │───▶│   检索策略      │
│   研究问题      │    │   PICO生成      │    │   查询优化      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PubMed API    │───▶│   文献筛选      │───▶│   数据处理      │
│   文献检索      │    │   并发处理      │    │   期刊匹配      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   结果缓存      │───▶│   Excel生成     │───▶│   最终报告      │
│   去重处理      │    │   格式化输出    │    │   用户交付      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 文件结构
```
pubmed_meta_search/
├── main.py                 # 主程序入口
├── setup.sh               # 快速安装脚本
├── .env                   # 环境变量配置
├── modules/               # 核心模块
│   ├── __init__.py
│   ├── gemini_client.py   # AI客户端
│   ├── pubmed_client.py   # PubMed API客户端
│   ├── data_processor.py  # 数据处理模块
│   ├── utils.py          # 工具函数
│   └── 2024影响因子+2025年中科院分区.csv
├── 说明文档/              # 文档目录
│   ├── USER_GUIDE.txt     # 用户指南
│   ├── TECHNICAL_GUIDE.md # 技术指南
│   ├── requirements.txt   # 依赖列表
│   └── 使用说明.html      # HTML说明
├── pico_results.xlsx      # 输出结果
├── pico_standards.txt     # PICO标准缓存
├── retrieved_pmids.csv    # 已检索PMID记录
└── app.log               # 应用日志
```

---

## 3. 模块详解

### 3.1 main.py - 主控制器
**职责**: 工作流调度、状态管理、并发控制

**核心函数**:
```python
def run_workflow(user_query: str) -> None:
    """主工作流程控制器"""
    # 1. 初始化客户端
    # 2. 生成/加载PICO标准
    # 3. 执行检索循环
    # 4. 并发筛选文献
    # 5. 生成最终报告

def screen_article(article_data: dict, pico_standard: str, 
                  user_query: str, attempt_number: int, 
                  current_results_count: int) -> dict:
    """单篇文献筛选任务"""
    # 并发执行的独立任务单元
```

**关键特性**:
- 🔄 自适应循环控制
- 🧵 多线程并发处理
- 💾 状态持久化
- 📊 实时进度监控

### 3.2 modules/gemini_client.py - AI引擎
**职责**: AI模型交互、提示词工程、智能分析

**模型配置**:
```python
# PICO生成: gemini-2.5-pro (最高准确性)
self.model_pro_25 = genai.GenerativeModel('gemini-2.5-pro')

# 查询生成和文献筛选: gemini-2.5-flash-lite (快速高效)
self.model_flash_lite = genai.GenerativeModel('gemini-2.5-flash-lite')
```

**核心方法**:
```python
def generate_pico_standard(self, user_query: str) -> str:
    """生成PICO标准 - 使用2.5-pro模型"""

def generate_pubmed_query(self, user_query: str, pico_standard: str) -> str:
    """生成检索式 - 使用2.5-flash-lite模型"""

def extract_article_info(self, abstract: str, pico_standard: str, 
                        user_query: str) -> str:
    """文献筛选 - 使用2.5-flash-lite模型"""
```

**性能优化**:
- ⚡ 快速响应和高质量输出
- 🎯 高准确率的文献筛选
- 🔄 智能重试机制
- 📝 结构化JSON输出

### 3.3 modules/pubmed_client.py - PubMed接口
**职责**: PubMed API交互、数据获取、错误处理

**核心改进**:
```python
def efetch_by_pmids(self, pmid_list: list, retmax: str = "600") -> str:
    """使用POST请求获取文献详情，避免URL长度限制"""
    # 解决了414 Request-URI Too Long错误
    # 支持一次性获取600篇文献
```

**关键特性**:
- 🔄 两阶段检索策略
- 📊 PMID去重机制
- 🚀 POST请求优化
- ⏱️ 智能重试机制

### 3.4 modules/data_processor.py - 数据处理
**职责**: XML解析、数据转换、Excel生成、期刊匹配

**核心功能**:
```python
def parse_pubmed_xml(self, xml_content: str) -> List[Dict]:
    """解析PubMed XML数据"""

def generate_formatted_excel(self, articles_data: List[Dict], 
                            output_file: str) -> None:
    """生成格式化Excel报告"""
```

**数据丰富化**:
- 📊 期刊影响因子匹配
- 🏆 中科院分区信息
- 🔗 PubMed链接生成
- 🎨 专业格式化

---

## 4. 工作流程

### 4.1 完整工作流程
```
用户输入研究问题 → AI生成PICO标准 → 生成PubMed检索式 → 执行PubMed搜索 
→ 获取PMID列表 → 过滤已处理PMID → 批量获取文献详情 → 并发筛选文献 
→ 提取关键信息 → 期刊信息匹配 → 生成Excel报告 → 保存结果和状态
```

### 4.2 自适应策略
```python
# 动态调整筛选严格度
if attempt_number <= 2:
    # 前两轮：相对宽松，确保召回率
    screening_threshold = "medium"
elif current_results_count < 50:
    # 结果不足：降低门槛
    screening_threshold = "relaxed"
else:
    # 结果充足：提高质量
    screening_threshold = "strict"
```

### 4.3 并发处理机制
```python
with ThreadPoolExecutor(max_workers=5) as executor:
    futures = []
    for article in articles_to_screen:
        future = executor.submit(screen_article, article, ...)
        futures.append(future)
    
    # 收集结果
    for future in as_completed(futures):
        result = future.result()
        if result['accepted']:
            accepted_articles.append(result)
```

---

## 5. AI模型配置

### 5.1 模型选择策略
基于大量测试，系统采用以下模型配置：

| 任务类型 | 模型 | 原因 |
|---------|------|------|
| PICO生成 | gemini-2.5-pro | 最高准确性，复杂推理能力强 |
| 查询生成 | gemini-2.5-flash-lite | 速度快61.9%，准确率90% |
| 文献筛选 | gemini-2.5-flash-lite | 高并发性能，质量优秀 |

### 5.2 模型特点
- **Gemini 2.5-pro**: 用于PICO标准生成，提供最高准确性和复杂推理能力
- **Gemini 2.5-flash-lite**: 用于查询生成和文献筛选，快速响应且质量优秀
- **智能分工**: 根据任务特点选择最适合的模型，平衡速度和质量

---

## 6. 数据处理

### 6.1 XML解析策略
```python
def parse_pubmed_xml(self, xml_content: str) -> List[Dict]:
    """健壮的XML解析，处理各种边界情况"""
    try:
        root = ET.fromstring(xml_content)
        articles = []
        
        for article in root.findall('.//PubmedArticle'):
            # 安全提取各字段，处理缺失值
            article_data = self._extract_article_safely(article)
            articles.append(article_data)
            
        return articles
    except ET.ParseError as e:
        logger.error(f"XML解析错误: {e}")
        return []
```

### 6.2 期刊信息匹配
```python
def _match_journal_info(self, journal_name: str, issn: str) -> dict:
    """多策略期刊信息匹配"""
    # 策略1: 精确ISSN匹配
    if issn and issn in self.journal_dict:
        return self.journal_dict[issn]
    
    # 策略2: 期刊名称模糊匹配
    normalized_name = self._normalize_journal_name(journal_name)
    for key, value in self.journal_dict.items():
        if normalized_name in self._normalize_journal_name(value.get('期刊名称', '')):
            return value
    
    # 策略3: 返回默认值
    return {'影响因子': 'N/A', '分区': 'N/A'}
```

---

## 7. 性能优化

### 7.1 缓存机制
```python
# PICO标准缓存
def save_pico_standard(pico_standard: str, filename: str = "pico_standards.txt"):
    """保存PICO标准，避免重复生成"""

# PMID去重缓存  
def load_existing_pmids(filename: str = "retrieved_pmids.csv") -> set:
    """加载已处理的PMID，避免重复处理"""
```

### 7.2 并发优化
```python
# 最优线程数配置
MAX_WORKERS = min(5, len(articles_to_screen))

# 批量处理策略
BATCH_SIZE = 600  # 每批处理的文献数量
```

---

## 8. 故障排除

### 8.1 常见错误及解决方案

#### 8.1.1 API相关错误
```python
# 错误: 414 Request-URI Too Long
# 解决: 已改用POST请求
def efetch_by_pmids(self, pmid_list: list) -> str:
    response = requests.post(endpoint, data=data, timeout=60)

# 错误: API密钥无效
# 解决: 检查.env文件配置
GEMINI_API_KEY=your_actual_api_key_here
```

#### 8.1.2 权限错误
```bash
# 错误: Permission denied
# 解决: 设置执行权限
chmod +x main.py
chmod +x setup.sh
```

#### 8.1.3 依赖包错误
```bash
# 错误: ModuleNotFoundError
# 解决: 安装依赖
pip3 install -r 说明文档/requirements.txt
```

### 8.2 日志分析
```python
# 日志级别配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)

# 关键日志点
logger.info("开始执行PubMed检索")
logger.warning(f"检索到0篇新文献，尝试优化检索式")
logger.error(f"API调用失败: {e}")
```

---

## 9. 开发指南

### 9.1 环境搭建
```bash
# 1. 克隆项目
git clone <repository_url>
cd pubmed_meta_search

# 2. 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 3. 安装依赖
pip install -r 说明文档/requirements.txt

# 4. 配置环境变量
cp .env.example .env
# 编辑.env文件，添加API密钥

# 5. 运行测试
python3 main.py "测试查询"
```

### 9.2 代码规范
```python
# 1. 函数文档字符串
def generate_pico_standard(self, user_query: str) -> str:
    """
    生成PICO标准
    
    Args:
        user_query: 用户研究问题
        
    Returns:
        str: 格式化的PICO标准文本
        
    Raises:
        Exception: API调用失败时抛出异常
    """

# 2. 类型注解
from typing import List, Dict, Optional

def parse_articles(self, xml_content: str) -> List[Dict[str, str]]:
    pass

# 3. 错误处理
try:
    result = api_call()
except requests.exceptions.RequestException as e:
    logger.error(f"API调用失败: {e}")
    raise
```

---

## 📞 技术支持

### 联系方式
- **技术文档**: 本文档
- **用户指南**: `USER_GUIDE.txt`
- **问题反馈**: 查看日志文件 `app.log`

### 版本信息
- **当前版本**: v2.0.0
- **Python要求**: 3.9+
- **主要依赖**: requests, pandas, openpyxl, google-generativeai

### 更新日志
- **v2.0.0**: 模型优化，性能提升61.9%
- **v1.5.0**: 添加POST请求支持，解决URL长度限制
- **v1.0.0**: 初始版本发布

---

*本技术指南持续更新中，如有疑问请参考用户指南或查看应用日志。*

#!/bin/bash
# PubMed Meta Search 项目设置脚本
# 运行此脚本来设置正确的文件权限

echo "🔧 设置 PubMed Meta Search 项目..."

# 检查 main.py 是否存在
if [ ! -f "main.py" ]; then
    echo "❌ 错误: main.py 文件不存在"
    echo "请确保您在正确的项目目录中运行此脚本"
    exit 1
fi

# 设置执行权限
chmod +x main.py
echo "✅ 已设置 main.py 执行权限"

# 检查 Python 环境
if command -v python3 &> /dev/null; then
    echo "✅ Python3 已安装"
    python3 --version
else
    echo "❌ 警告: 未找到 Python3，请先安装 Python3"
fi

# 检查必要的依赖
echo "📦 检查依赖包..."
python3 -c "
import sys
required_packages = ['requests', 'pandas', 'openpyxl', 'google-generativeai', 'tenacity', 'python-dotenv']
missing_packages = []

for package in required_packages:
    try:
        __import__(package.replace('-', '_'))
        print(f'✅ {package}')
    except ImportError:
        missing_packages.append(package)
        print(f'❌ {package} (缺失)')

if missing_packages:
    print(f'\n📥 安装缺失的包:')
    print(f'pip3 install {\" \".join(missing_packages)}')
else:
    print('\n🎉 所有依赖包都已安装!')
" 2>/dev/null || echo "⚠️  无法检查依赖包，请手动确认"

# 检查 .env 文件
if [ -f ".env" ]; then
    echo "✅ .env 配置文件存在"
else
    echo "⚠️  .env 配置文件不存在，请创建并添加 GOOGLE_API_KEY"
fi

echo ""
echo "🎉 设置完成！现在您可以运行："
echo "   python3 main.py \"您的查询\""
echo ""

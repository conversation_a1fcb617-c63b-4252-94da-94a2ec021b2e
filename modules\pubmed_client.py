import logging
import os
import requests
from tenacity import retry, stop_after_attempt, wait_exponential
from typing import Dict, Optional

# Configure the logger for this module
logger = logging.getLogger(__name__)

class PubMedClient:
    """
    A client for interacting with the NCBI PubMed E-utilities API.

    This class provides methods to search for articles (Esearch) and fetch
    their detailed data (Efetch), including robust error handling and retries.
    """
    BASE_URL = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/"

    def __init__(self):
        """
        Initializes the PubMed client.
        
        Retrieves the PubMed API key from environment variables if available.
        """
        self.api_key = os.getenv("PUBMED_API_KEY")
        if self.api_key:
            logger.info("PubMed client initialized with an API key.")
        else:
            logger.info("PubMed client initialized without an API key. Consider adding one for higher request rates.")

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def esearch(self, term: str, retmax: str = "100000") -> Optional[Dict]:
        """
        Performs a search on PubMed using the Esearch utility.

        Args:
            term: The search term or query.
            retmax: Maximum number of PMIDs to retrieve (default: 100000 to get all results)

        Returns:
            A dictionary containing the 'webenv', 'querykey', 'count', and 'idlist'
            from the search result, or None if the search fails.
        """
        # Check query length - PubMed has limits
        if len(term) > 4000:
            logger.warning(f"Query length ({len(term)}) exceeds recommended limit. Truncating...")
            term = term[:4000]
            logger.warning(f"Truncated query: {term}")

        endpoint = f"{self.BASE_URL}esearch.fcgi"
        params = {
            "db": "pubmed",
            "term": term,
            "usehistory": "y",
            "retmode": "json",
            "retmax": retmax,  # Now configurable to get all PMIDs
        }
        if self.api_key:
            params["api_key"] = self.api_key

        try:
            logger.info(f"Executing Esearch for term: {term[:100]}... (retmax={retmax})")
            response = requests.post(endpoint, data=params, timeout=30)

            # Log response details for debugging
            logger.debug(f"Response status code: {response.status_code}")
            logger.debug(f"Response headers: {dict(response.headers)}")

            response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)

            # Check if response content is empty
            if not response.text.strip():
                logger.error("Received empty response from PubMed API")
                logger.error(f"Response status: {response.status_code}")
                logger.error(f"Response headers: {dict(response.headers)}")
                raise ValueError("Empty response from PubMed API")

            # Check for maintenance page
            if "Maintenance in progress" in response.text or "maintenance" in response.text.lower():
                logger.error("🚧 PubMed/NCBI is currently under maintenance!")
                logger.error("The service is temporarily unavailable. Please try again later.")
                if "24+ hours" in response.text:
                    logger.error("⏰ Maintenance may last 24+ hours according to the maintenance page.")
                raise ValueError("PubMed API is under maintenance")

            # Check if response is HTML instead of JSON
            if response.text.strip().startswith('<?xml') or response.text.strip().startswith('<html'):
                logger.error("Received HTML/XML response instead of JSON from PubMed API")
                logger.error("This usually indicates a service error or maintenance.")
                logger.error(f"Response content preview: {response.text[:300]}")
                raise ValueError("PubMed API returned HTML instead of JSON")

            # Log first 200 characters of response for debugging
            logger.debug(f"Response content preview: {response.text[:200]}")

            data = response.json()
            esearch_result = data.get("esearchresult")

            if not esearch_result:
                logger.error("Esearch result is missing from the API response.")
                logger.error(f"Full response: {response.text}")
                return None

            count = int(esearch_result.get("count", 0))
            idlist = esearch_result.get("idlist", [])
            logger.info(f"Esearch found {count} articles, retrieved {len(idlist)} PMIDs.")

            return {
                "webenv": esearch_result.get("webenv"),
                "querykey": esearch_result.get("querykey"),
                "count": count,
                "idlist": idlist,
            }

        except requests.exceptions.RequestException as e:
            logger.error(f"An HTTP error occurred during Esearch: {e}", exc_info=True)
            logger.error(f"Request URL: {endpoint}")
            logger.error(f"Request params: {params}")
            raise
        except ValueError as e: # Catches JSON decoding errors
            logger.error(f"Failed to decode JSON from Esearch response: {e}", exc_info=True)
            logger.error(f"Response status code: {response.status_code}")
            logger.error(f"Response content: {response.text}")
            logger.error(f"Request URL: {endpoint}")
            logger.error(f"Request params: {params}")
            raise

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def efetch(self, webenv: str, query_key: str, retmax: str = "600") -> Optional[str]:
        """
        Fetches detailed article information from PubMed using the Efetch utility.

        Args:
            webenv: The WebEnv identifier from a previous Esearch call.
            query_key: The QueryKey identifier from a previous Esearch call.
            retmax: Maximum number of articles to fetch (default: 600).

        Returns:
            A string containing the raw XML data of the fetched articles,
            or None if the fetch fails.
        """
        endpoint = f"{self.BASE_URL}efetch.fcgi"
        params = {
            "db": "pubmed",
            "webenv": webenv,
            "query_key": query_key,
            "retmode": "xml",
            "rettype": "abstract",
            "retmax": retmax,
        }
        if self.api_key:
            params["api_key"] = self.api_key

        try:
            logger.info(f"Executing Efetch with WebEnv: {webenv} (retmax={retmax})")
            response = requests.get(endpoint, params=params, timeout=60) # Longer timeout for potentially large data
            response.raise_for_status()

            logger.info("Efetch completed successfully.")
            return response.text

        except requests.exceptions.RequestException as e:
            logger.error(f"An HTTP error occurred during Efetch: {e}", exc_info=True)
            raise

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def efetch_by_pmids(self, pmid_list: list, retmax: str = "600") -> Optional[str]:
        """
        Fetches detailed article information from PubMed using specific PMIDs.
        Uses POST request to avoid URL length limits.

        Args:
            pmid_list: List of PMIDs to fetch.
            retmax: Maximum number of articles to fetch (default: 600).

        Returns:
            A string containing the raw XML data of the fetched articles,
            or None if the fetch fails.
        """
        if not pmid_list:
            logger.warning("Empty PMID list provided to efetch_by_pmids")
            return None

        # Limit the number of PMIDs to fetch
        pmids_to_fetch = pmid_list[:int(retmax)]
        pmid_string = ",".join(pmids_to_fetch)

        endpoint = f"{self.BASE_URL}efetch.fcgi"
        data = {
            "db": "pubmed",
            "id": pmid_string,
            "retmode": "xml",
            "rettype": "abstract",
        }
        if self.api_key:
            data["api_key"] = self.api_key

        try:
            logger.info(f"Executing Efetch for {len(pmids_to_fetch)} specific PMIDs using POST request")
            response = requests.post(endpoint, data=data, timeout=60)
            response.raise_for_status()

            logger.info("Efetch by PMIDs completed successfully.")
            return response.text

        except requests.exceptions.RequestException as e:
            logger.error(f"An HTTP error occurred during Efetch by PMIDs: {e}", exc_info=True)
            raise


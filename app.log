2025-08-05 17:34:29 - root - INFO - Logging configured successfully.
2025-08-05 17:34:29 - root - INFO - Workflow started for query: 'COVID-19的远期并发症有哪些'
2025-08-05 17:34:29 - modules.gemini_client - INFO - Google Gemini client initialized successfully with gemini-2.5-flash-lite for query and screening tasks.
2025-08-05 17:34:29 - modules.pubmed_client - INFO - PubMed client initialized with an API key.
2025-08-05 17:34:29 - root - INFO - PMID file not found at 'retrieved_pmids.csv'. Starting with an empty set.
2025-08-05 17:34:29 - root - INFO - Loaded 0 previously retrieved PMIDs.
2025-08-05 17:34:29 - root - INFO - Step 1: Generating initial PICO standard...
2025-08-05 17:34:29 - modules.gemini_client - INFO - Generating PICO standard for query: 'COVID-19的远期并发症有哪些'
2025-08-05 17:34:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 17:44:26 - modules.gemini_client - ERROR - An error occurred while communicating with the Gemini API: Timeout of 600.0s exceeded, last exception: 503 failed to connect to all addresses; last error: UNKNOWN: ipv6:%5B2404:6800:4012:6::200a%5D:443: socket is null
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\google\api_core\grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\grpc\_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\grpc\_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\grpc\_channel.py", line 440, in result
    raise self
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\grpc\_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\grpc\_channel.py", line 1192, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\grpc\_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "failed to connect to all addresses; last error: UNKNOWN: ipv6:%5B2404:6800:4012:6::200a%5D:443: socket is null"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"failed to connect to all addresses; last error: UNKNOWN: ipv6:%5B2404:6800:4012:6::200a%5D:443: socket is null"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 failed to connect to all addresses; last error: UNKNOWN: ipv6:%5B2404:6800:4012:6::200a%5D:443: socket is null

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\pubmed_meta_search\pubmed_meta_search\modules\gemini_client.py", line 65, in _generate_content
    response = model.generate_content(prompt, safety_settings=safety_settings)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
               ^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\google\api_core\retry\retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 600.0s exceeded, last exception: 503 failed to connect to all addresses; last error: UNKNOWN: ipv6:%5B2404:6800:4012:6::200a%5D:443: socket is null
2025-08-05 17:44:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 17:49:34 - root - INFO - Logging configured successfully.
2025-08-05 17:49:34 - root - INFO - Workflow started for query: 'COVID-19的远期并发症有哪些'
2025-08-05 17:49:34 - modules.gemini_client - INFO - Google Gemini client initialized successfully with gemini-2.5-flash-lite for query and screening tasks.
2025-08-05 17:49:34 - modules.pubmed_client - INFO - PubMed client initialized with an API key.
2025-08-05 17:49:34 - root - INFO - PMID file not found at 'retrieved_pmids.csv'. Starting with an empty set.
2025-08-05 17:49:34 - root - INFO - Loaded 0 previously retrieved PMIDs.
2025-08-05 17:49:34 - root - INFO - Step 1: Generating initial PICO standard...
2025-08-05 17:49:34 - modules.gemini_client - INFO - Generating PICO standard for query: 'COVID-19的远期并发症有哪些'
2025-08-05 17:49:34 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 17:52:02 - root - INFO - Logging configured successfully.
2025-08-05 17:52:02 - root - INFO - Workflow started for query: 'COVID-19的远期并发症有哪些'
2025-08-05 17:52:02 - modules.gemini_client - INFO - Google Gemini client initialized successfully with gemini-2.5-flash-lite for query and screening tasks.
2025-08-05 17:52:02 - modules.pubmed_client - INFO - PubMed client initialized with an API key.
2025-08-05 17:52:02 - root - INFO - PMID file not found at 'retrieved_pmids.csv'. Starting with an empty set.
2025-08-05 17:52:02 - root - INFO - Loaded 0 previously retrieved PMIDs.
2025-08-05 17:52:02 - root - INFO - Step 1: Generating initial PICO standard...
2025-08-05 17:52:02 - modules.gemini_client - INFO - Generating PICO standard for query: 'COVID-19的远期并发症有哪些'
2025-08-05 17:52:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
